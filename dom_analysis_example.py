import asyncio
from browser_use.browser import <PERSON><PERSON>er<PERSON>ession, BrowserProfile
from browser_use.dom.service import DomService

async def main():
    # Create browser session
    browser_session = BrowserSession(
        browser_profile=BrowserProfile(
            headless=False,
            viewport={'width': 1280, 'height': 720},
        )
    )
    
    await browser_session.start()
    
    try:
        # Navigate to a page with interactive elements
        await browser_session.navigate('https://google.com')
        page = await browser_session.get_current_page()
        
        # Use the DOM service directly (same as the Agent uses internally)
        dom_service = DomService(page)
        
        # Get clickable elements analysis
        dom_state = await dom_service.get_clickable_elements(
            highlight_elements=True,  # Visually highlight elements
            focus_element=-1,         # No specific focus
            viewport_expansion=0      # No viewport expansion
        )
        
        print(f"Found {len(dom_state.selector_map)} interactive elements")
        
        # Analyze the DOM tree
        def print_element_tree(element, depth=0):
            indent = "  " * depth
            text = element.get_all_text_till_next_clickable_element(max_depth=1)
            text_preview = text[:50] + "..." if len(text) > 50 else text
            
            print(f"{indent}{element.tag_name} - {text_preview}")
            
            # Print first few children only to avoid spam
            for i, child in enumerate(element.children[:3]):
                print_element_tree(child, depth + 1)
            if len(element.children) > 3:
                print(f"{indent}  ... and {len(element.children) - 3} more children")
        
        print("\nDOM Tree Structure:")
        print_element_tree(dom_state.element_tree)
        
        # Print clickable elements with their indices
        print(f"\nClickable Elements (first 10):")
        for i, (index, element) in enumerate(dom_state.selector_map.items()):
            if i >= 10:  # Limit output
                break
            text = element.get_all_text_till_next_clickable_element(max_depth=2)
            text_preview = text[:100] + "..." if len(text) > 100 else text
            print(f"  Index {index}: {element.tag_name} - '{text_preview}'")
        
        # Get browser state summary (what the Agent would see)
        browser_state = await browser_session.get_browser_state_with_recovery(
            cache_clickable_elements_hashes=False,
            include_screenshot=True
        )
        
        print(f"\nBrowser State Summary:")
        print(f"  URL: {browser_state.url}")
        print(f"  Title: {browser_state.title}")
        print(f"  Interactive elements: {len(browser_state.selector_map)}")
        print(f"  Screenshot included: {browser_state.screenshot is not None}")
        print(f"  Page dimensions: {browser_state.page_info.page_width}x{browser_state.page_info.page_height}" if browser_state.page_info else "No page info")
        
        # You can also click elements programmatically using their index
        if dom_state.selector_map:
            first_element_index = next(iter(dom_state.selector_map.keys()))
            print(f"\nWould click element at index {first_element_index}")
            # Uncomment to actually click:
            # await browser_session._click_element_node(dom_state.selector_map[first_element_index])
        
    finally:
        await browser_session.close()

if __name__ == '__main__':
    asyncio.run(main())
