import asyncio
import json
from browser_use.browser import B<PERSON>erSession, BrowserProfile

async def scrape_hacker_news():
    """Scrape Hacker News front page without using LLM"""
    
    browser_session = BrowserSession(
        browser_profile=BrowserProfile(
            headless=True,  # Run in background
            viewport={'width': 1280, 'height': 720},
        )
    )
    
    await browser_session.start()
    
    try:
        # Navigate to Hacker News
        await browser_session.navigate('https://news.ycombinator.com')
        page = await browser_session.get_current_page()
        
        # Extract data using JavaScript
        stories = await page.evaluate("""
            () => {
                const stories = [];
                const storyRows = document.querySelectorAll('.athing');
                
                storyRows.forEach((row, index) => {
                    if (index >= 10) return; // Limit to first 10 stories
                    
                    const titleElement = row.querySelector('.titleline > a');
                    const scoreRow = row.nextElementSibling;
                    const scoreElement = scoreRow ? scoreRow.querySelector('.score') : null;
                    const commentsElement = scoreRow ? scoreRow.querySelector('a[href*="item?id="]') : null;
                    
                    if (titleElement) {
                        stories.push({
                            rank: index + 1,
                            title: titleElement.textContent.trim(),
                            url: titleElement.href,
                            score: scoreElement ? parseInt(scoreElement.textContent) : 0,
                            comments: commentsElement ? commentsElement.textContent : '0 comments'
                        });
                    }
                });
                
                return stories;
            }
        """)
        
        print("Top 10 Hacker News Stories:")
        print("=" * 50)
        
        for story in stories:
            print(f"{story['rank']}. {story['title']}")
            print(f"   Score: {story['score']} | {story['comments']}")
            print(f"   URL: {story['url']}")
            print()
        
        # Save to JSON
        with open('hackernews_stories.json', 'w') as f:
            json.dump(stories, f, indent=2)
        
        print(f"Saved {len(stories)} stories to hackernews_stories.json")
        
        return stories
        
    finally:
        await browser_session.close()

async def scrape_with_interaction():
    """Example of scraping that requires interaction (clicking, scrolling)"""
    
    browser_session = BrowserSession(
        browser_profile=BrowserProfile(
            headless=False,  # Visible for demonstration
            viewport={'width': 1280, 'height': 720},
        )
    )
    
    await browser_session.start()
    
    try:
        # Navigate to a page that requires interaction
        await browser_session.navigate('https://quotes.toscrape.com')
        page = await browser_session.get_current_page()
        
        all_quotes = []
        
        # Scrape multiple pages
        for page_num in range(1, 4):  # First 3 pages
            print(f"Scraping page {page_num}...")
            
            # Extract quotes from current page
            quotes = await page.evaluate("""
                () => {
                    const quotes = [];
                    document.querySelectorAll('.quote').forEach(quote => {
                        const text = quote.querySelector('.text').textContent;
                        const author = quote.querySelector('.author').textContent;
                        const tags = Array.from(quote.querySelectorAll('.tag')).map(tag => tag.textContent);
                        
                        quotes.push({ text, author, tags });
                    });
                    return quotes;
                }
            """)
            
            all_quotes.extend(quotes)
            print(f"Found {len(quotes)} quotes on page {page_num}")
            
            # Try to click "Next" button
            try:
                next_button = await page.query_selector('.next > a')
                if next_button:
                    await next_button.click()
                    await page.wait_for_load_state('networkidle')
                else:
                    print("No more pages")
                    break
            except Exception as e:
                print(f"Could not navigate to next page: {e}")
                break
        
        print(f"\nTotal quotes collected: {len(all_quotes)}")
        
        # Save results
        with open('quotes.json', 'w') as f:
            json.dump(all_quotes, f, indent=2)
        
        print("Saved quotes to quotes.json")
        
        return all_quotes
        
    finally:
        await browser_session.close()

async def main():
    print("=== Scraping Hacker News ===")
    await scrape_hacker_news()
    
    print("\n=== Scraping Quotes (with interaction) ===")
    await scrape_with_interaction()

if __name__ == '__main__':
    asyncio.run(main())
