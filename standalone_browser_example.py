import asyncio
from browser_use.browser import BrowserSession, BrowserProfile

async def main():
    # Create a browser session without any Agent/LLM
    browser_session = BrowserSession(
        browser_profile=BrowserProfile(
            headless=False,  # Set to True for headless mode
            viewport={'width': 1280, 'height': 720},
            user_data_dir='~/.config/browseruse/profiles/standalone',
        )
    )
    
    # Start the browser
    await browser_session.start()
    
    try:
        # Navigate to a website
        page = await browser_session.navigate('https://google.com')
        print(f"Navigated to: {page.url}")
        
        # Get page title
        title = await page.title()
        print(f"Page title: {title}")
        
        # Take a screenshot
        await page.screenshot(path='example_screenshot.png')
        print("Screenshot saved!")
        
        # Execute JavaScript
        result = await browser_session.execute_javascript('document.title')
        print(f"JS result: {result}")
        
        # Get page HTML
        html = await browser_session.get_page_html()
        print(f"HTML length: {len(html)} characters")
        
        # Navigate to another page
        await browser_session.navigate('https://httpbin.org/json')
        
        # Get cookies
        cookies = await browser_session.get_cookies()
        print(f"Found {len(cookies)} cookies")
        
        # Create a new tab
        new_page = await browser_session.create_new_tab('https://github.com')
        print(f"New tab created: {new_page.url}")
        
        # Switch between tabs
        tabs_info = await browser_session.get_tabs_info()
        print(f"Open tabs: {len(tabs_info)}")
        for i, tab in enumerate(tabs_info):
            print(f"  Tab {i}: {tab.title} - {tab.url}")
        
        # Switch to first tab
        await browser_session.switch_to_tab(0)
        current_page = await browser_session.get_current_page()
        print(f"Current tab: {current_page.url}")
        
    finally:
        # Clean up
        await browser_session.close()

if __name__ == '__main__':
    asyncio.run(main())
