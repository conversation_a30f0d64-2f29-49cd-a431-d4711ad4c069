import asyncio
from browser_use.browser import B<PERSON>er<PERSON>ession, BrowserProfile

async def automate_form_filling():
    """Example of automated form filling without LLM"""
    
    browser_session = BrowserSession(
        browser_profile=BrowserProfile(
            headless=False,  # Visible to see the automation
            viewport={'width': 1280, 'height': 720},
        )
    )
    
    await browser_session.start()
    
    try:
        # Navigate to a form page (using httpbin for testing)
        await browser_session.navigate('https://httpbin.org/forms/post')
        page = await browser_session.get_current_page()
        
        print("Filling out form...")
        
        # Fill form fields
        await page.fill('input[name="custname"]', '<PERSON>')
        await page.fill('input[name="custtel"]', '******-123-4567')
        await page.fill('input[name="custemail"]', '<EMAIL>')
        
        # Select from dropdown
        await page.select_option('select[name="size"]', 'large')
        
        # Check radio button
        await page.check('input[name="topping"][value="bacon"]')
        
        # Check checkbox
        await page.check('input[name="delivery"]')
        
        # Fill textarea
        await page.fill('textarea[name="comments"]', 'This is an automated test form submission.')
        
        print("Form filled successfully!")
        
        # Take screenshot before submission
        await page.screenshot(path='form_before_submit.png')
        
        # Submit form
        await page.click('input[type="submit"]')
        
        # Wait for response
        await page.wait_for_load_state('networkidle')
        
        # Take screenshot after submission
        await page.screenshot(path='form_after_submit.png')
        
        # Get the response
        response_text = await page.text_content('body')
        print("Form submission response received")
        print("Response preview:", response_text[:200] + "..." if len(response_text) > 200 else response_text)
        
    finally:
        await browser_session.close()

async def automate_search_and_extract():
    """Example of search automation and data extraction"""
    
    browser_session = BrowserSession(
        browser_profile=BrowserProfile(
            headless=False,
            viewport={'width': 1280, 'height': 720},
        )
    )
    
    await browser_session.start()
    
    try:
        # Navigate to DuckDuckGo
        await browser_session.navigate('https://duckduckgo.com')
        page = await browser_session.get_current_page()
        
        # Search for something
        search_query = "browser automation python"
        await page.fill('input[name="q"]', search_query)
        await page.press('input[name="q"]', 'Enter')
        
        # Wait for results
        await page.wait_for_selector('.results', timeout=10000)
        
        print(f"Search completed for: {search_query}")
        
        # Extract search results
        results = await page.evaluate("""
            () => {
                const results = [];
                const resultElements = document.querySelectorAll('.results .result');
                
                resultElements.forEach((element, index) => {
                    if (index >= 5) return; // Limit to first 5 results
                    
                    const titleElement = element.querySelector('.result__title a');
                    const snippetElement = element.querySelector('.result__snippet');
                    const urlElement = element.querySelector('.result__url');
                    
                    if (titleElement) {
                        results.push({
                            title: titleElement.textContent.trim(),
                            snippet: snippetElement ? snippetElement.textContent.trim() : '',
                            url: titleElement.href
                        });
                    }
                });
                
                return results;
            }
        """)
        
        print(f"\nFound {len(results)} search results:")
        print("=" * 50)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['title']}")
            print(f"   {result['snippet']}")
            print(f"   {result['url']}")
            print()
        
        # Click on first result
        if results:
            first_result_selector = '.results .result:first-child .result__title a'
            await page.click(first_result_selector)
            await page.wait_for_load_state('networkidle')
            
            new_title = await page.title()
            new_url = page.url
            print(f"Navigated to: {new_title}")
            print(f"URL: {new_url}")
        
    finally:
        await browser_session.close()

async def main():
    print("=== Form Automation Example ===")
    await automate_form_filling()
    
    print("\n=== Search and Extract Example ===")
    await automate_search_and_extract()

if __name__ == '__main__':
    asyncio.run(main())
